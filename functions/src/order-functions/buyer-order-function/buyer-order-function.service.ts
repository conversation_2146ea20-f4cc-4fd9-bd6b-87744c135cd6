import * as admin from "firebase-admin";
import { UserType } from "../../mikerudenko/marketplace-shared";
import {
  requireAuthentication,
  validateBuyerOwnership,
  validateOrderCreationParams,
  validatePurchaseParams,
  getUserData,
} from "../../services/auth-middleware";
import { createOrder } from "../../services/order-creation-service";
import { processPurchase } from "../../services/purchase-flow-service";
import { validateTelegramIdForOrderOperation } from "../../services/telegram-validation-service";

export interface CreateOrderAsBuyerParams {
  buyerId: string;
  collectionId: string;
  price: number;
}

export interface MakePurchaseAsBuyerParams {
  buyerId: string;
  orderId: string;
}

export async function validateCreateOrderRequest(
  request: any,
  params: CreateOrderAsBuyerParams
): Promise<any> {
  const authRequest = requireAuthentication(request);
  validateOrderCreationParams(params, UserType.BUYER);
  validateBuyerOwnership(authRequest, params.buyerId);

  const user = await getUserData(authRequest.auth.uid);
  validateTelegramIdForOrderOperation(user, "create orders");

  return authRequest;
}

export async function validatePurchaseRequest(
  request: any,
  params: MakePurchaseAsBuyerParams
): Promise<any> {
  const authRequest = requireAuthentication(request);
  validatePurchaseParams(params, UserType.BUYER);
  validateBuyerOwnership(authRequest, params.buyerId);

  // Validate user has telegram ID for purchase operations
  const user = await getUserData(authRequest.auth.uid);
  validateTelegramIdForOrderOperation(user, "make purchases");

  return authRequest;
}

export async function createBuyerOrder(
  params: CreateOrderAsBuyerParams
): Promise<any> {
  const db = admin.firestore();
  const { buyerId, collectionId, price } = params;

  return await createOrder(db, {
    userId: buyerId,
    collectionId,
    price,
    gift: null,
    userType: UserType.BUYER,
    secondaryMarketPrice: null,
  });
}

export async function processBuyerPurchase(
  params: MakePurchaseAsBuyerParams
): Promise<any> {
  const db = admin.firestore();
  const { buyerId, orderId } = params;

  return await processPurchase(db, {
    userId: buyerId,
    orderId,
    userType: UserType.BUYER,
  });
}
