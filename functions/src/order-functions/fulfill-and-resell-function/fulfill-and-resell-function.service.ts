import * as admin from "firebase-admin";
import {
  OrderEntity,
  OrderStatus,
  UserType,
  ORDERS_COLLECTION_NAME,
  OrderGift,
} from "../../mikerudenko/marketplace-shared";
import { createOrderForMarketCollection } from "../../services/order-creation-service";
import {
  throwOrderNotFound,
  throwNotOrderBuyer,
  throwInvalidOrderStatus,
} from "./fulfill-and-resell-function.error-handler";

export interface FulfillAndResellParams {
  orderId: string;
  price: number;
  userId: string;
}

export interface FulfillAndResellResult {
  success: boolean;
  message: string;
  originalOrderId: string;
  newOrderId: string;
  price: number;
  lockAmount: number;
}

export async function fulfillAndResellOrder({
  orderId,
  price,
  userId,
}: FulfillAndResellParams): Promise<FulfillAndResellResult> {
  const db = admin.firestore();

  // Get the order
  const orderDoc = await db
    .collection(ORDERS_COLLECTION_NAME)
    .doc(orderId)
    .get();

  if (!orderDoc.exists) {
    throwOrderNotFound();
  }

  const order = { id: orderDoc.id, ...orderDoc.data() } as OrderEntity;

  // Validate that the current user is the buyer of this order
  if (order.buyerId !== userId) {
    throwNotOrderBuyer();
  }

  // Validate that the order status is "gift_sent_to_relayer"
  if (order.status !== OrderStatus.GIFT_SENT_TO_RELAYER) {
    throwInvalidOrderStatus();
  }

  // Step 1: Update the original order status to fulfilled
  await db.collection(ORDERS_COLLECTION_NAME).doc(orderId).update({
    status: OrderStatus.FULFILLED,
    updatedAt: admin.firestore.FieldValue.serverTimestamp(),
  });

  // Step 2: Create a new order where the current user is the seller
  // Since gift is in relayer, use MARKET collection logic (no seller collateral)
  const newOrderResult = await createOrderForMarketCollection(db, {
    userId,
    collectionId: order.collectionId,
    price,
    gift: order.gift as OrderGift,
    userType: UserType.SELLER,
    secondaryMarketPrice: null,
  });

  return {
    success: true,
    message:
      "Order fulfilled and resell order created successfully. No collateral required since gift is in relayer.",
    originalOrderId: orderId,
    newOrderId: newOrderResult.orderId,
    price,
    lockAmount: 0, // No lock amount since no collateral required
  };
}
