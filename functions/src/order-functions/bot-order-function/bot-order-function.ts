import { onCall } from "firebase-functions/v2/https";
import { commonFunctionsConfig } from "../../constants";
import {
  completePurchaseForBot,
  sendGiftToRelayerForBot,
} from "./bot-order-function.service";
import {
  logGetOrderError,
  logGetUserOrdersError,
  logCompletePurchaseError,
  logSendGiftError,
} from "./bot-order-function.logger";
import { throwBotOrderInternalError } from "./bot-order-function.error-handler";
import { GiftFromBot } from "@/mikerudenko/marketplace-shared";
import {
  getOrderByIdForBot,
  getUserOrdersForBot,
} from "@/services/order-query.service";

export const getOrderByIdByBot = onCall<{
  orderId: string;
  botToken: string;
}>(commonFunctionsConfig, async (request) => {
  const { orderId, botToken } = request.data;

  try {
    return await getOrderByIdForBot({ orderId, botToken });
  } catch (error) {
    logGetOrderError({
      error,
      orderId: request.data.orderId,
    });
    throwBotOrderInternalError((error as any).message);
  }
});

export const getUserOrdersByBot = onCall<{
  userId?: string;
  tgId?: string;
  botToken: string;
}>(commonFunctionsConfig, async (request) => {
  const { userId, tgId, botToken } = request.data;

  try {
    return await getUserOrdersForBot({ userId, tgId, botToken });
  } catch (error) {
    logGetUserOrdersError({
      error,
      userId: request.data.userId,
    });
    throwBotOrderInternalError((error as any).message);
  }
});

export const completePurchaseByBot = onCall<{
  orderId: string;
  botToken: string;
}>(commonFunctionsConfig, async (request) => {
  const { orderId, botToken } = request.data;

  try {
    return await completePurchaseForBot({ orderId, botToken });
  } catch (error) {
    logCompletePurchaseError({
      error,
      orderId: request.data.orderId,
    });
    throwBotOrderInternalError((error as any).message);
  }
});

export const sendGiftToRelayerByBot = onCall<{
  orderId: string;
  gift: GiftFromBot;
  botToken: string;
}>(commonFunctionsConfig, async (request) => {
  const { orderId, gift, botToken } = request.data;

  try {
    return await sendGiftToRelayerForBot({ orderId, gift, botToken });
  } catch (error) {
    logSendGiftError({
      error,
      orderId: request.data.orderId,
    });
    throwBotOrderInternalError((error as any).message);
  }
});
