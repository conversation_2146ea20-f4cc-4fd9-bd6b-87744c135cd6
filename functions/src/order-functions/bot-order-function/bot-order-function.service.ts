import * as admin from "firebase-admin";
import {
  APP_USERS_COLLECTION,
  AppDate,
  GiftFromBot,
  GiftStatus,
  ORDERS_COLLECTION_NAME,
  OrderEntity,
  OrderStatus,
  UserEntity,
  formatDateToFirebaseTimestamp,
} from "../../mikerudenko/marketplace-shared";
import { validateBotTokenSimple } from "../../services/bot-validation.service";
import { createGift, updateGiftOwnership } from "../../services/gift-service";
import { transferNetAmountToSeller } from "../../services/purchase-fee-processing-service";
import { safeMultiply } from "../../utils";
import {
  throwBotTokenRequired,
  throwInvalidBotToken,
  throwInvalidOrderId,
  throwInvalidOrderStatus,
  throwOrderNotFound,
} from "./bot-order-function.error-handler";

export function validateBotToken(botToken?: string): void {
  try {
    validateBotTokenSimple(botToken);
  } catch (error) {
    if (
      error instanceof Error &&
      error.message.includes("Bot token is required")
    ) {
      throwBotTokenRequired();
    } else {
      throwInvalidBotToken();
    }
  }
}

export function validateOrderId(orderId?: string): void {
  if (!orderId) {
    throwInvalidOrderId();
  }
}

export async function completePurchaseForBot(params: {
  orderId: string;
  botToken: string;
}) {
  const { orderId, botToken } = params;

  validateOrderId(orderId);
  validateBotToken(botToken);

  const db = admin.firestore();
  const orderDoc = await db
    .collection(ORDERS_COLLECTION_NAME)
    .doc(orderId)
    .get();

  if (!orderDoc.exists) {
    throwOrderNotFound();
  }

  const order = { id: orderDoc.id, ...orderDoc.data() } as OrderEntity;

  if (order.status !== OrderStatus.GIFT_SENT_TO_RELAYER) {
    throwInvalidOrderStatus();
  }

  await orderDoc.ref.update({
    status: OrderStatus.FULFILLED,
    updatedAt: formatDateToFirebaseTimestamp(
      admin.firestore.Timestamp.now() as AppDate
    ),
  });

  // Transfer gift ownership to buyer when order becomes fulfilled
  if (order.giftId && order.buyerId) {
    // Get buyer's telegram ID
    const buyerDoc = await db
      .collection(APP_USERS_COLLECTION)
      .doc(order.buyerId)
      .get();

    if (buyerDoc.exists) {
      const buyer = buyerDoc.data() as UserEntity;
      if (buyer.tg_id) {
        await updateGiftOwnership(
          order.giftId,
          buyer.tg_id,
          GiftStatus.WITHDRAWN
        );
      }
    }
  }

  return {
    success: true,
    message: "Purchase completed successfully.",
    order: {
      ...order,
      status: OrderStatus.FULFILLED,
    },
  };
}

export async function sendGiftToRelayerForBot(params: {
  orderId: string;
  gift: GiftFromBot;
  botToken: string;
}) {
  const { orderId, gift, botToken } = params;

  validateOrderId(orderId);
  validateBotToken(botToken);

  const db = admin.firestore();
  const orderDoc = await db
    .collection(ORDERS_COLLECTION_NAME)
    .doc(orderId)
    .get();

  if (!orderDoc.exists) {
    throwOrderNotFound();
  }

  const order = { id: orderDoc.id, ...orderDoc.data() } as OrderEntity;

  // Get seller's telegram ID for gift ownership
  let sellerTgId: string | undefined;
  if (order.sellerId) {
    const sellerDoc = await db
      .collection(APP_USERS_COLLECTION)
      .doc(order.sellerId)
      .get();

    if (sellerDoc.exists) {
      const seller = sellerDoc.data() as UserEntity;
      sellerTgId = seller.tg_id;
    }
  }

  if (!sellerTgId) {
    throw new Error("Seller telegram ID not found");
  }

  // Handle two different order statuses
  if (order.status === OrderStatus.PAID) {
    // Logic for paid orders - send gift to relayer with accounting
    const batch = db.batch();

    // Create gift in separate collection
    const giftId = await createGift(
      gift,
      sellerTgId,
      order.collectionId,
      batch
    );

    // Update order status and reference gift by ID
    batch.update(orderDoc.ref, {
      giftId,
      gift, // Keep for backward compatibility
      status: OrderStatus.GIFT_SENT_TO_RELAYER,
      updatedAt: formatDateToFirebaseTimestamp(
        admin.firestore.Timestamp.now() as AppDate
      ),
    });

    // Transfer funds from buyer's locked balance to seller
    if (order.buyerId && order.sellerId) {
      // Calculate net amount using order's fee structure
      const purchaseFeeRate = order.fees?.purchase_fee || 0;
      const netSellerAmount = safeMultiply(
        order.price,
        1 - purchaseFeeRate / 10000
      ); // Convert BPS to decimal

      // Use reusable function for money transfer
      await transferNetAmountToSeller(order, netSellerAmount, order.buyerId);
    }

    await batch.commit();

    return {
      success: true,
      message: "Gift sent to relayer successfully.",
      order: {
        ...order,
        giftId,
        gift,
        status: OrderStatus.GIFT_SENT_TO_RELAYER,
      },
    };
  } else if (order.status === OrderStatus.CREATED) {
    // New logic for created orders - just update with gift and activate
    // Create gift in separate collection
    const giftId = await createGift(gift, sellerTgId, order.collectionId);

    await orderDoc.ref.update({
      giftId,
      gift, // Keep for backward compatibility
      status: OrderStatus.ACTIVE,
      updatedAt: formatDateToFirebaseTimestamp(
        admin.firestore.Timestamp.now() as AppDate
      ),
    });

    return {
      success: true,
      message: "Gift added and order activated successfully.",
      order: {
        ...order,
        giftId,
        gift,
        status: OrderStatus.ACTIVE,
      },
    };
  } else {
    throwInvalidOrderStatus();
  }
}
