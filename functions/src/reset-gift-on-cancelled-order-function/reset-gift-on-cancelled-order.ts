import { onCall, HttpsError } from "firebase-functions/v2/https";
import { setGiftAsWithdrawnOnCancelledOrder } from "./reset-gift-on-cancelled-order.service";
import {
  logResetGiftFunctionCalled,
  logResetGiftCompleted,
} from "./reset-gift-on-cancelled-order.logger";
import { handleResetGiftFunctionError } from "./reset-gift-on-cancelled-order.error-handler";

interface ResetGiftOnCancelledOrderRequest {
  userId: string;
  botToken: string;
  orderId: string;
}

export const setGiftAsWithdrawnOnCancelledOrderByBot = onCall(
  { cors: true },
  async (request) => {
    try {
      logResetGiftFunctionCalled({
        userId: request.data?.userId,
        orderId: request.data?.orderId,
        hasBotToken: !!request.data?.botToken,
      });

      // Validate request data
      if (!request.data) {
        throw new HttpsError("invalid-argument", "Request data is required");
      }

      const { userId, botToken, orderId } =
        request.data as ResetGiftOnCancelledOrderRequest;

      if (!userId || !botToken || !orderId) {
        throw new HttpsError(
          "invalid-argument",
          "userId, botToken, and orderId are required"
        );
      }

      const result = await setGiftAsWithdrawnOnCancelledOrder({
        userId,
        botToken,
        orderId,
      });

      logResetGiftCompleted({
        userId,
        orderId,
        success: result.success,
      });

      return result;
    } catch (error) {
      return handleResetGiftFunctionError(error);
    }
  }
);
