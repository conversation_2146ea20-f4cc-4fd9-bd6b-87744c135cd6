import * as admin from "firebase-admin";
import {
  ORDERS_COLLECTION_NAME,
  OrderEntity,
  OrderStatus,
} from "../mikerudenko/marketplace-shared";
import { convertDocsToEntities } from "../utils/firestore-converter";
import {
  validateOrderId,
  validateBotToken,
} from "@/order-functions/bot-order-function";
import { processCancelledOrdersWithGifts } from "@/order-functions/bot-order-function/services/cancelled-orders.service";
import { filterOrdersByMarketCollections } from "./collection.service";
import { getUserTelegramIds, resolveUserId } from "./user-lookup.service";

export interface OrderQueryParams {
  userId: string;
}

export interface OrderQueryResult {
  docs: admin.firestore.QueryDocumentSnapshot[];
}

export async function queryPaidOrdersAwaitingGift(
  params: OrderQueryParams
): Promise<OrderQueryResult> {
  const db = admin.firestore();
  const query = await db
    .collection(ORDERS_COLLECTION_NAME)
    .where("sellerId", "==", params.userId)
    .where("status", "==", OrderStatus.PAID)
    .get();

  return { docs: query.docs };
}

export async function queryCreatedOrdersNeedingActivation(
  params: OrderQueryParams
): Promise<OrderQueryResult> {
  const db = admin.firestore();
  const query = await db
    .collection(ORDERS_COLLECTION_NAME)
    .where("sellerId", "==", params.userId)
    .where("status", "==", OrderStatus.CREATED)
    .get();

  return { docs: query.docs };
}

export async function queryCancelledOrders(
  params: OrderQueryParams
): Promise<OrderQueryResult> {
  const db = admin.firestore();
  const query = await db
    .collection(ORDERS_COLLECTION_NAME)
    .where("sellerId", "==", params.userId)
    .where("status", "==", OrderStatus.CANCELLED)
    .get();

  return { docs: query.docs };
}

export async function queryBuyOrders(
  params: OrderQueryParams
): Promise<OrderQueryResult> {
  const db = admin.firestore();
  const query = await db
    .collection(ORDERS_COLLECTION_NAME)
    .where("buyerId", "==", params.userId)
    .where("status", "==", OrderStatus.GIFT_SENT_TO_RELAYER)
    .get();

  return { docs: query.docs };
}

export async function queryOrderById(
  orderId: string
): Promise<admin.firestore.DocumentSnapshot> {
  const db = admin.firestore();
  return await db.collection(ORDERS_COLLECTION_NAME).doc(orderId).get();
}

export function convertDocsToOrders(
  docs: admin.firestore.QueryDocumentSnapshot[]
): OrderEntity[] {
  return convertDocsToEntities<OrderEntity>(docs);
}

export async function getOrderByIdForBot(params: {
  orderId: string;
  botToken: string;
}) {
  const { orderId, botToken } = params;

  validateOrderId(orderId);
  validateBotToken(botToken);

  const orderDoc = await queryOrderById(orderId);

  if (!orderDoc.exists) {
    return {
      success: false,
      order: null,
      message: "Order not found.",
    };
  }

  const order = { id: orderDoc.id, ...orderDoc.data() } as OrderEntity;

  const { buyer_tg_id, seller_tg_id } = await getUserTelegramIds(order);

  return {
    success: true,
    order: {
      ...order,
      buyer_tg_id,
      seller_tg_id,
    },
    message: "Order retrieved successfully.",
  };
}

export async function getUserOrdersForBot(params: {
  userId?: string;
  tgId?: string;
  botToken: string;
}) {
  const { userId, tgId, botToken } = params;

  validateBotToken(botToken);

  const userLookupResult = await resolveUserId({ userId, tgId });

  if (!userLookupResult.success) {
    return {
      success: false,
      orders: [],
      sellOrders: [],
      paidOrdersAwaitingGift: [],
      createdOrdersNeedingActivation: [],
      cancelledOrdersWithGifts: [],
      buyOrders: [],
      count: 0,
      sellOrdersCount: 0,
      paidOrdersAwaitingGiftCount: 0,
      createdOrdersNeedingActivationCount: 0,
      cancelledOrdersWithGiftsCount: 0,
      buyOrdersCount: 0,
      userId: "",
      message: userLookupResult.message || "User not found.",
    };
  }

  const targetUserId = userLookupResult.userId!;

  // Query all order types using the new service
  const [
    paidOrdersResult,
    createdOrdersResult,
    cancelledOrdersResult,
    buyOrdersResult,
  ] = await Promise.all([
    queryPaidOrdersAwaitingGift({ userId: targetUserId }),
    queryCreatedOrdersNeedingActivation({ userId: targetUserId }),
    queryCancelledOrders({ userId: targetUserId }),
    queryBuyOrders({ userId: targetUserId }),
  ]);

  const paidOrdersAwaitingGift = convertDocsToOrders(paidOrdersResult.docs);
  const createdOrdersTemp = convertDocsToOrders(createdOrdersResult.docs);
  const cancelledOrdersTemp = convertDocsToOrders(cancelledOrdersResult.docs);
  const buyOrders = convertDocsToOrders(buyOrdersResult.docs);

  const createdOrdersNeedingActivation = await filterOrdersByMarketCollections(
    createdOrdersTemp
  );

  const cancelledOrdersWithGifts = await processCancelledOrdersWithGifts(
    cancelledOrdersTemp
  );

  const sellOrders = [
    ...paidOrdersAwaitingGift,
    ...createdOrdersNeedingActivation,
    ...cancelledOrdersWithGifts,
  ];
  const allOrders = [...sellOrders, ...buyOrders];

  return {
    success: true,
    orders: allOrders,
    sellOrders,
    paidOrdersAwaitingGift, // paid orders waiting for gift
    createdOrdersNeedingActivation, // created orders for MARKET collections
    cancelledOrdersWithGifts, // cancelled orders with gifts for refund
    buyOrders,
    count: allOrders.length,
    sellOrdersCount: sellOrders.length,
    paidOrdersAwaitingGiftCount: paidOrdersAwaitingGift.length,
    createdOrdersNeedingActivationCount: createdOrdersNeedingActivation.length,
    cancelledOrdersWithGiftsCount: cancelledOrdersWithGifts.length,
    buyOrdersCount: buyOrders.length,
    userId: targetUserId,
    message: "Orders retrieved successfully.",
  };
}
