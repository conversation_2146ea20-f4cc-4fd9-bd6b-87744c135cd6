import * as admin from "firebase-admin";
import { TRANSACTION_DESCRIPTION_INTL_KEYS } from "../../constants/transaction-description-intl-keys";
import { safeMultiply, safeSubtract, bpsToDecimal } from "../../utils";
import {
  addFunds,
  addFundsWithHistory,
  updateUserBalance,
  validateSufficientFunds,
} from "../balance-service/balance-service";
import { createTransactionRecord } from "../transaction-history-service/transaction-history-service";
import {
  AppConfigEntity,
  UserEntity,
  TxType,
  APP_CONFIG_COLLECTION,
  APP_CONFIG_DOC_ID,
  MARKETPLACE_REVENUE_USER_ID,
  Role,
  APP_USERS_COLLECTION,
} from "../../mikerudenko/marketplace-shared";
import {
  logAppConfigNotFound,
  logAdminUserNotFound,
  logFeeApplied,
  logFeeServiceError,
  logReferralFeeApplied,
  logCustomReferral<PERSON>ee,
  logOrder<PERSON>efer<PERSON><PERSON><PERSON>,
  logReferrerNotFound,
  logTotalFeeApplied,
} from "./fee-service.logger";

export async function getAppConfig() {
  try {
    const db = admin.firestore();
    const doc = await db
      .collection(APP_CONFIG_COLLECTION)
      .doc(APP_CONFIG_DOC_ID)
      .get();

    if (!doc.exists) {
      logAppConfigNotFound();
      return {
        deposit_fee: 0,
        withdrawal_fee: 0,
        referrer_fee: 0,
        cancel_order_fee: 0,
        purchase_fee: 0,
        buyer_lock_percentage: 0,
        seller_lock_percentage: 0,
        resell_purchase_fee: 0,
        resell_purchase_fee_for_seller: 0,
        min_deposit_amount: 0,
        min_withdrawal_amount: 0,
        max_withdrawal_amount: 0,
        min_secondary_market_price: 0,
        fixed_cancel_order_fee: 0,
        lock_period: 21, // Default 21 days lock period
      };
    }

    return doc.data() as AppConfigEntity;
  } catch (error) {
    logFeeServiceError({
      error,
      operation: "app_config_fetch",
    });
    throw error;
  }
}

export function calculateFeeAmount(amount: number, feeBps: number) {
  if (!feeBps || feeBps <= 0) {
    return 0;
  }
  // BPS = basis points (1 BPS = 0.01%)
  return safeMultiply(amount, bpsToDecimal(feeBps));
}

export async function getAdminUser() {
  try {
    const db = admin.firestore();
    const adminQuery = await db
      .collection(APP_USERS_COLLECTION)
      .where("role", "==", Role.ADMIN)
      .limit(1)
      .get();

    if (adminQuery.empty) {
      logAdminUserNotFound();
      return null;
    }

    const adminDoc = adminQuery.docs[0];
    return {
      id: adminDoc.id,
      ...adminDoc.data(),
    } as UserEntity;
  } catch (error) {
    logFeeServiceError({
      error,
      operation: "admin_user_lookup",
    });
    throw error;
  }
}

export async function applyFeeToMarketplaceRevenue(params: {
  feeAmount: number;
  feeType: string;
}) {
  const { feeAmount, feeType } = params;
  if (feeAmount <= 0) {
    return;
  }

  try {
    await addFunds(MARKETPLACE_REVENUE_USER_ID, feeAmount);
    logFeeApplied({
      feeAmount,
      feeType,
      userId: MARKETPLACE_REVENUE_USER_ID,
    });
  } catch (error) {
    logFeeServiceError({
      error,
      operation: "marketplace_revenue",
      amount: feeAmount,
    });
    throw error;
  }
}

export async function applyDepositFee(params: { depositAmount: number }) {
  const { depositAmount } = params;
  try {
    const config = await getAppConfig();
    if (!config?.deposit_fee) {
      return depositAmount;
    }

    // Deposit fee is now a static TON value, not BPS
    const feeAmount = config.deposit_fee;
    if (feeAmount <= 0 || feeAmount >= depositAmount) {
      return depositAmount;
    }

    const netAmount = safeSubtract(depositAmount, feeAmount);

    await applyFeeToMarketplaceRevenue({
      feeAmount,
      feeType: "deposit",
    });

    logFeeApplied({
      feeAmount,
      feeType: "deposit",
      netAmount,
    });

    return netAmount;
  } catch (error) {
    logFeeServiceError({
      error,
      operation: "deposit_fee",
      amount: depositAmount,
    });
    throw error;
  }
}

export async function applyFixedCancelOrderFee(userId: string) {
  try {
    const config = await getAppConfig();
    if (!config?.fixed_cancel_order_fee) {
      return 0;
    }

    // Fixed cancel order fee is a static TON value, not BPS
    const feeAmount = config.fixed_cancel_order_fee;
    if (feeAmount <= 0) {
      return 0;
    }

    await validateSufficientFunds({
      userId,
      amount: feeAmount,
      operation: "fixed cancel order fee",
    });

    await updateUserBalance({ userId, sumChange: -feeAmount, lockedChange: 0 });

    // Record transaction history for penalty payment
    await createTransactionRecord({
      userId,
      txType: TxType.CANCELATION_FEE,
      amount: feeAmount,
      descriptionIntlKey:
        TRANSACTION_DESCRIPTION_INTL_KEYS.FIXED_CANCELLATION_FEE_PENALTY,
      descriptionIntlParams: {
        amount: feeAmount.toString(),
      },
      isReceivingCompensation: false,
    });

    await applyFeeToMarketplaceRevenue({
      feeAmount,
      feeType: "fixed_cancel_order",
    });

    logFeeApplied({
      feeAmount,
      feeType: "fixed_cancel_order",
      userId,
    });

    return feeAmount;
  } catch (error) {
    logFeeServiceError({
      error,
      operation: "fixed_cancel_order_fee",
      userId,
    });
    throw error;
  }
}

export async function applyResellPurchaseFee(params: {
  buyerId: string;
  amount: number;
  resellPurchaseFeeBPS: number;
}) {
  const { buyerId, amount, resellPurchaseFeeBPS } = params;
  try {
    if (!resellPurchaseFeeBPS || resellPurchaseFeeBPS <= 0) {
      return { totalFee: 0, referralFee: 0, marketplaceFee: 0 };
    }

    const totalFeeAmount = calculateFeeAmount(amount, resellPurchaseFeeBPS);
    if (totalFeeAmount <= 0) {
      return { totalFee: 0, referralFee: 0, marketplaceFee: 0 };
    }

    // Validate buyer has sufficient funds for total fee
    await validateSufficientFunds({
      userId: buyerId,
      amount: totalFeeAmount,
      operation: "resell purchase fee",
    });

    // Deduct total fee from buyer
    await updateUserBalance({
      userId: buyerId,
      sumChange: -totalFeeAmount,
      lockedChange: 0,
    });

    // No referral fees for reselling - all fee goes to marketplace
    const marketplaceFeeAmount = totalFeeAmount;

    // Apply fee to marketplace revenue
    await applyFeeToMarketplaceRevenue({
      feeAmount: marketplaceFeeAmount,
      feeType: "resell_purchase",
    });

    logTotalFeeApplied({
      feeAmount: totalFeeAmount,
      feeType: "resell_purchase_total",
      referralFee: 0,
      marketplaceFee: marketplaceFeeAmount,
    });

    return {
      totalFee: totalFeeAmount,
      referralFee: 0,
      marketplaceFee: marketplaceFeeAmount,
    };
  } catch (error) {
    logFeeServiceError({
      error,
      operation: "resell_purchase_fee",
    });
    throw error;
  }
}

export async function applyPurchaseFeeWithReferralFromOrder(params: {
  buyerId: string;
  amount: number;
  referralId?: string;
  purchaseFeeBPS: number;
  referrerFeeBPS: number;
}) {
  const { buyerId, amount, referralId, purchaseFeeBPS, referrerFeeBPS } =
    params;
  try {
    if (!purchaseFeeBPS || purchaseFeeBPS <= 0) {
      return { totalFee: 0, referralFee: 0, marketplaceFee: 0 };
    }

    const totalFeeAmount = calculateFeeAmount(amount, purchaseFeeBPS);
    if (totalFeeAmount <= 0) {
      return { totalFee: 0, referralFee: 0, marketplaceFee: 0 };
    }

    // Validate buyer has sufficient funds for total fee
    await validateSufficientFunds({
      userId: buyerId,
      amount: totalFeeAmount,
      operation: "purchase fee with referral from order",
    });

    // Deduct total fee from buyer
    await updateUserBalance({
      userId: buyerId,
      sumChange: -totalFeeAmount,
      lockedChange: 0,
    });

    let referralFeeAmount = 0;
    let marketplaceFeeAmount = totalFeeAmount;

    // If there's a referral ID, check for custom or default referrer fee
    if (referralId && referrerFeeBPS > 0) {
      const db = admin.firestore();
      const referrerQuery = await db
        .collection(APP_USERS_COLLECTION)
        .where("id", "==", referralId)
        .limit(1)
        .get();

      if (!referrerQuery.empty) {
        const referrerDoc = referrerQuery.docs[0];
        const referrerId = referrerDoc.id;
        const referrerData = referrerDoc.data();

        // Check for custom referral fee first, then fall back to order fee
        let referrerFeeRate = 0;
        if (
          referrerData.referral_fee !== undefined &&
          referrerData.referral_fee > 0
        ) {
          // Use custom referral fee
          referrerFeeRate = referrerData.referral_fee;
          logCustomReferralFee({
            referrerFeeRate,
            referrerId,
          });
        } else {
          // Use referral fee from order
          referrerFeeRate = referrerFeeBPS;
          logOrderReferralFee({
            referrerFeeRate,
            referrerId,
          });
        }

        if (referrerFeeRate > 0) {
          referralFeeAmount = calculateFeeAmount(amount, referrerFeeRate);
          marketplaceFeeAmount = safeSubtract(
            totalFeeAmount,
            referralFeeAmount
          );

          // Add referral fee to referrer's balance
          await addFundsWithHistory({
            userId: referrerId,
            amount: referralFeeAmount,
            txType: TxType.REFERRAL_FEE,
            descriptionIntlKey:
              TRANSACTION_DESCRIPTION_INTL_KEYS.REFERRAL_FEE_FROM_PURCHASE,
            descriptionIntlParams: {
              amount: referralFeeAmount.toString(),
            },
          });

          logReferralFeeApplied({
            feeAmount: referralFeeAmount,
            feeType: "purchase_referral",
            referrerFeeRate,
            referrerId,
            referralId,
          });
        }
      } else {
        logReferrerNotFound({ referralId });
      }
    }

    // Apply remaining fee to marketplace revenue
    if (marketplaceFeeAmount > 0) {
      await applyFeeToMarketplaceRevenue({
        feeAmount: marketplaceFeeAmount,
        feeType: "purchase",
      });
    }

    logTotalFeeApplied({
      feeAmount: totalFeeAmount,
      feeType: "purchase_total",
      referralFee: referralFeeAmount,
      marketplaceFee: marketplaceFeeAmount,
    });

    return {
      totalFee: totalFeeAmount,
      referralFee: referralFeeAmount,
      marketplaceFee: marketplaceFeeAmount,
    };
  } catch (error) {
    logFeeServiceError({
      error,
      operation: "purchase_fee_with_referral",
    });
    throw error;
  }
}

export async function applyWithdrawFee(userId: string, withdrawAmount: number) {
  try {
    const config = await getAppConfig();
    if (!config?.withdrawal_fee) {
      return 0;
    }

    // Withdraw fee is now a static TON value, not BPS
    const feeAmount = config.withdrawal_fee;
    if (feeAmount <= 0 || feeAmount >= withdrawAmount) {
      return 0;
    }

    await applyFeeToMarketplaceRevenue({
      feeAmount,
      feeType: "withdrawal",
    });

    logFeeApplied({
      feeAmount,
      feeType: "withdrawal",
      userId,
    });

    return feeAmount;
  } catch (error) {
    logFeeServiceError({
      error,
      operation: "withdrawal_fee",
      userId,
      amount: withdrawAmount,
    });
    throw error;
  }
}
