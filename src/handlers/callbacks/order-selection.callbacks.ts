import {
  getUserOrdersByTgId,
  completePurchaseByBot,
} from "../../firebase-service";
import { setUserSession } from "../../services/session";
import {
  createOrderActionsKeyboard,
  createOrderBackKeyboard,
  createOrderCompletionKeyboard,
} from "../../utils/keyboards";
import { handleGiftToRelayer } from "../../middleware/business-connection";
import { CALLBACK_MESSAGES } from "./constants/messages";
import { MOCK_ORDER_GIFT } from "../../app.constants";
import { log } from "../../utils/logger";
import { OrderStatus } from "../../mikerudenko/marketplace-shared";

const isGiftSimulation = () => process.env.GIFT_SIMULATION === "true";

const handleDevModeOrder = async (
  ctx: any,
  order: any,
  orderId: string,
  isPaidOrderAwaitingGift: boolean,
  isCreatedOrderNeedingActivation: boolean,
  isBuyer: boolean,
  isSeller: boolean
) => {
  let message = `📦 Order #${order.number}\n\n`;

  if (isBuyer && order.status === OrderStatus.GIFT_SENT_TO_RELAYER) {
    message += CALLBACK_MESSAGES.GIFT_READY_FOR_DELIVERY;
    message += `\n\n🔧 DEV MODE: Simulating gift retrieval...`;

    try {
      await completePurchaseByBot(orderId);
      message += CALLBACK_MESSAGES.DEV_PURCHASE_SUCCESS;
    } catch (error) {
      log.error("Error completing purchase in dev mode", error, {
        operation: "dev_complete_purchase",
        orderId,
        chatId: ctx.chat?.id,
        userId: ctx.from?.id,
      });
      message += CALLBACK_MESSAGES.DEV_PURCHASE_ERROR;
    }

    ctx.reply(message, createOrderBackKeyboard());
  } else if (
    isSeller &&
    (isPaidOrderAwaitingGift || isCreatedOrderNeedingActivation)
  ) {
    const isActivation = isCreatedOrderNeedingActivation;
    message += CALLBACK_MESSAGES.DEV_ORDER_READY(isActivation);

    try {
      await handleGiftToRelayer(
        ctx,
        orderId,
        MOCK_ORDER_GIFT,
        ctx.chat?.id ?? 0
      );
      message += CALLBACK_MESSAGES.DEV_SUCCESS(isActivation);
    } catch (error) {
      log.error("Error sending gift in dev mode", error, {
        operation: "dev_send_gift",
        orderId,
        chatId: ctx.chat?.id,
        userId: ctx.from?.id,
      });
      message += CALLBACK_MESSAGES.DEV_ERROR(isActivation);
    }

    ctx.reply(message, createOrderBackKeyboard());
  } else {
    // For other order statuses in dev mode
    message += `Status: ${order.status.toUpperCase()}\n\n${
      CALLBACK_MESSAGES.DEV_NO_ACTIONS
    }`;
    ctx.reply(message, createOrderBackKeyboard());
  }
};

const handleProductionModeOrder = async (params: {
  ctx: any;
  order: any;
  orderId: string;
  isPaidOrderAwaitingGift: boolean;
  isCreatedOrderNeedingActivation: boolean;
  isCancelledOrderWithGifts: boolean;
  isBuyer: boolean;
  isSeller: boolean;
  tgId: string;
}) => {
  const {
    ctx,
    order,
    orderId,
    isPaidOrderAwaitingGift,
    isCreatedOrderNeedingActivation,
    isCancelledOrderWithGifts,
    isBuyer,
    isSeller,
    tgId,
  } = params;
  let message = `📦 Order #${order.number}\n\n`;

  if (isBuyer && order.status === OrderStatus.GIFT_SENT_TO_RELAYER) {
    // For buyers with gifts ready for delivery
    message += CALLBACK_MESSAGES.GIFT_READY_FOR_DELIVERY;
    await setUserSession(tgId, { pendingOrderId: orderId });
    ctx.reply(message, createOrderBackKeyboard());
  } else if (isSeller && isPaidOrderAwaitingGift) {
    // Paid orders waiting for gift to be sent to relayer
    message += CALLBACK_MESSAGES.GROUP1_ORDER_READY;
    ctx.reply(message, createOrderActionsKeyboard(orderId));
  } else if (isSeller && isCreatedOrderNeedingActivation) {
    // Created orders for MARKET collections (need gift to activate)
    message += CALLBACK_MESSAGES.GROUP2_ORDER_READY;
    ctx.reply(message, createOrderActionsKeyboard(orderId));
  } else if (isSeller && isCancelledOrderWithGifts) {
    // Cancelled orders with gifts for refund
    message += CALLBACK_MESSAGES.GROUP3_ORDER_READY;
    ctx.reply(message, createOrderBackKeyboard());
  } else {
    // For other order statuses in production mode
    message += `Status: ${order.status.toUpperCase()}\n\n`;
    const statusMessage =
      CALLBACK_MESSAGES.ORDER_STATUS_MESSAGES[
        order.status as keyof typeof CALLBACK_MESSAGES.ORDER_STATUS_MESSAGES
      ];
    if (statusMessage) {
      message += statusMessage;
    }
    ctx.reply(message, createOrderBackKeyboard());
  }
};

export const handleOrderSelectionCallback = async (ctx: any) => {
  try {
    ctx.answerCbQuery();

    const orderId = ctx.match[1];
    const tgId = ctx.from?.id?.toString();

    if (!tgId || !orderId) {
      ctx.reply(CALLBACK_MESSAGES.TELEGRAM_ID_ERROR);
      return;
    }

    // Get user orders to find the selected order
    const ordersResponse = await getUserOrdersByTgId(tgId);
    const order = ordersResponse.orders.find((o) => o.id === orderId);

    if (!order) {
      ctx.reply(CALLBACK_MESSAGES.ORDER_NOT_FOUND);
      return;
    }

    // Determine which group this order belongs to
    const isPaidOrderAwaitingGift = ordersResponse.paidOrdersAwaitingGift?.some(
      (o) => o.id === orderId
    );
    const isCreatedOrderNeedingActivation =
      ordersResponse.createdOrdersNeedingActivation?.some(
        (o) => o.id === orderId
      );
    const isCancelledOrderWithGifts =
      ordersResponse.cancelledOrdersWithGifts?.some((o) => o.id === orderId);

    // Check if user is buyer or seller for this order
    const isBuyer = order.buyerId === ordersResponse.userId;
    const isSeller = order.sellerId === ordersResponse.userId;

    // Handle based on mode
    if (isGiftSimulation()) {
      await handleDevModeOrder(
        ctx,
        order,
        orderId,
        isPaidOrderAwaitingGift,
        isCreatedOrderNeedingActivation,
        isBuyer,
        isSeller
      );
    } else {
      await handleProductionModeOrder({
        ctx,
        order,
        orderId,
        isPaidOrderAwaitingGift,
        isCreatedOrderNeedingActivation,
        isCancelledOrderWithGifts,
        isBuyer,
        isSeller,
        tgId,
      });
    }
  } catch (error) {
    log.error("Error handling order selection", error, {
      operation: "order_selection",
      chatId: ctx.chat?.id,
      userId: ctx.from?.id,
    });
    ctx.reply(CALLBACK_MESSAGES.GENERIC_ERROR, createOrderBackKeyboard());
  }
};

export const handleOrderCompletionCallback = async (ctx: any) => {
  try {
    ctx.answerCbQuery();

    const orderId = ctx.match[1];

    if (!orderId) {
      ctx.reply("❌ Unable to identify order. Please try again.");
      return;
    }

    ctx.reply(
      `🎁 Ready to Complete Order

Please send the gift to this @premrelayer now. You can send:`,
      createOrderCompletionKeyboard(orderId)
    );

    // Store the order ID for the next message from this user
    const userId = ctx.from?.id?.toString();
    if (userId && orderId) {
      await setUserSession(userId, { pendingOrderId: orderId });
    }
  } catch (error) {
    log.error("Error handling order completion", error, {
      operation: "order_completion",
      chatId: ctx.chat?.id,
      userId: ctx.from?.id,
    });
    ctx.reply("❌ Failed to prepare order completion. Please try again later.");
  }
};

export const handleReceiveGiftCallback = async (ctx: any) => {
  try {
    ctx.answerCbQuery();

    const userId = ctx.from?.id?.toString();

    if (!userId) {
      ctx.reply(CALLBACK_MESSAGES.TELEGRAM_ID_ERROR);
      return;
    }

    ctx.reply("🎁 Preparing your gift for delivery...");

    // Here we would implement the actual gift delivery logic
    // For now, we'll just mark the order as fulfilled
    // In a real implementation, this would involve the actual gift transfer mechanism

    ctx.reply(
      CALLBACK_MESSAGES.GIFT_RECEIVED_SUCCESS,
      createOrderBackKeyboard()
    );
  } catch (error) {
    log.error("Error receiving gift", error, {
      operation: "receive_gift",
      chatId: ctx.chat?.id,
      userId: ctx.from?.id,
    });
    ctx.reply(
      "❌ Failed to receive gift. Please try again later.",
      createOrderBackKeyboard()
    );
  }
};
