import { Context, Markup } from "telegraf";
import {
  getUserOrdersByTgId,
  formatOrderForDisplay,
  getGiftReadyOrders,
} from "../firebase-service";
import { createMarketplaceInlineKeyboard } from "../utils/keyboards";
import { MESSAGES, BUTTON_TEXTS } from "../constants/messages";
import {
  logUserBuyOrdersError,
  logUserSellOrdersError,
} from "./handlers.logger";

export const handleGetMyBuyOrdersButton = async (ctx: Context) => {
  try {
    const tgId = ctx.from?.id?.toString();
    if (!tgId) {
      ctx.reply(MESSAGES.TELEGRAM_ID_ERROR);
      return;
    }

    ctx.reply(MESSAGES.ORDERS.FETCHING_BUY);

    const ordersResponse = await getUserOrdersByTgId(tgId);

    if (!ordersResponse.success || ordersResponse.buyOrdersCount === 0) {
      ctx.reply(
        MESSAGES.ORDERS.NO_BUY_ORDERS,
        createMarketplaceInlineKeyboard()
      );
      return;
    }

    const buyOrders = ordersResponse.buyOrders;
    const giftReadyOrders = getGiftReadyOrders(buyOrders);

    let message = `${MESSAGES.ORDERS.BUY_ORDERS_TITLE(buyOrders.length)}

`;

    if (giftReadyOrders.length > 0) {
      message += `${MESSAGES.ORDERS.GIFTS_READY_FOR_DELIVERY(
        giftReadyOrders.length
      )}

`;
    }

    const orderButtons = giftReadyOrders
      .slice(0, 10)
      .map((order) => [
        Markup.button.callback(
          `${formatOrderForDisplay(order)}`,
          `order_${order.id}`
        ),
      ]);

    orderButtons.push([
      Markup.button.callback(BUTTON_TEXTS.OPEN_MARKETPLACE, "open_marketplace"),
    ]);

    if (buyOrders.length > 10) {
      message += `
${MESSAGES.ORDERS.SHOWING_LIMITED_ORDERS}`;
    }

    ctx.reply(message, Markup.inlineKeyboard(orderButtons));
  } catch (error) {
    logUserBuyOrdersError({
      error,
      userId: String(ctx.from?.id),
      chatId: String(ctx.chat?.id),
    });
    ctx.reply(
      MESSAGES.ORDERS.FETCH_ERROR_BUY,
      createMarketplaceInlineKeyboard()
    );
  }
};

export const handleGetMySellOrdersButton = async (ctx: Context) => {
  try {
    const tgId = ctx.from?.id?.toString();
    if (!tgId) {
      ctx.reply(MESSAGES.TELEGRAM_ID_ERROR);
      return;
    }

    ctx.reply(MESSAGES.ORDERS.FETCHING_SELL);

    const ordersResponse = await getUserOrdersByTgId(tgId);

    if (!ordersResponse.success || ordersResponse.sellOrdersCount === 0) {
      ctx.reply(
        MESSAGES.ORDERS.NO_SELL_ORDERS,
        createMarketplaceInlineKeyboard()
      );
      return;
    }

    const sellOrders = ordersResponse.sellOrders;
    const completableOrders = sellOrders.filter(
      (order) => order.status === "paid"
    );
    const giftReadyOrders = sellOrders.filter(
      (order) => order.status === "gift_sent_to_relayer"
    );

    let message = `${MESSAGES.ORDERS.SELL_ORDERS_TITLE(sellOrders.length)}

`;

    if (completableOrders.length > 0) {
      message += `${MESSAGES.ORDERS.ORDERS_READY_FOR_COMPLETION(
        completableOrders.length
      )}

`;
    }

    if (giftReadyOrders.length > 0) {
      message += `${MESSAGES.ORDERS.GIFTS_READY_FOR_DELIVERY(
        giftReadyOrders.length
      )}

`;
    }

    const orderButtons = sellOrders
      .slice(0, 10)
      .filter((order) => order.status === "paid")
      .map((order) => [
        Markup.button.callback(
          `${formatOrderForDisplay(order)}`,
          `order_${order.id}`
        ),
      ]);

    orderButtons.push([
      Markup.button.callback(BUTTON_TEXTS.OPEN_MARKETPLACE, "open_marketplace"),
    ]);

    if (sellOrders.length > 10) {
      message += `
${MESSAGES.ORDERS.SHOWING_LIMITED_ORDERS}`;
    }

    ctx.reply(message, Markup.inlineKeyboard(orderButtons));
  } catch (error) {
    logUserSellOrdersError({
      error,
      userId: String(ctx.from?.id),
      chatId: String(ctx.chat?.id),
    });
    ctx.reply(
      MESSAGES.ORDERS.FETCH_ERROR_SELL,
      createMarketplaceInlineKeyboard()
    );
  }
};

export const handleContactSupportButton = (ctx: Context) => {
  ctx.reply(MESSAGES.SUPPORT.CONTACT_INFO, createMarketplaceInlineKeyboard());
};
