import { Gift } from 'lucide-react';
import { useIntl } from 'react-intl';

import { Button } from '@/components/ui/button';

import type { ButtonConfig } from './user-order-card-button-config';

interface ActionButtonProps {
  config: ButtonConfig;
}

export function ActionButton({ config }: ActionButtonProps) {
  const { formatMessage: t } = useIntl();

  if (!config.show) return null;

  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    config.handler?.();
  };

  return (
    <Button
      {...{ [config.dataAttr]: true }}
      onClick={handleClick}
      className={`mt-2 w-full ${config.className} text-white text-sm py-2`}
    >
      <Gift className="w-4 h-4 mr-2" />
      {t(config.message)}
    </Button>
  );
}
