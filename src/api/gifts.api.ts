import { doc, getDoc } from 'firebase/firestore';

import {
  type GiftEntity,
  GIFTS_COLLECTION_NAME,
} from '@/mikerudenko/marketplace-shared';
import { firestore } from '@/root-context';

export async function getGiftById(giftId: string) {
  if (!giftId) {
    return null;
  }

  try {
    const giftRef = doc(firestore, GIFTS_COLLECTION_NAME, giftId);
    const giftSnap = await getDoc(giftRef);

    if (!giftSnap.exists()) {
      return null;
    }

    return {
      id: giftSnap.id,
      ...giftSnap.data(),
    } as GiftEntity;
  } catch (error) {
    console.error('Error fetching gift by ID:', error);
    return null;
  }
}
