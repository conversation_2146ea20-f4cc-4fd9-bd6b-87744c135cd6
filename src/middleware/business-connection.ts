import { Context } from "telegraf";
import { TelegramBusinessMessageContext } from "../app.constants";
import { handleActivateOrderWithGift } from "../flows/activate-order-with-gift-flow";
import { handleBuyerGetGift } from "../flows/buyer-get-gift-flow";
import { handleGetCancelledGift } from "../flows/get-cancelled-gift-flow";
import {
  handleSendGiftToPaidOrder,
  type FlowContext,
} from "../flows/send-gift-to-paid-order-flow";
import { getUserSession } from "../services/session";
import { getGiftToTransfer } from "../utils/business-connection-helpers";
import { log } from "../utils/logger";

export const businessConnectionMiddleware = async (
  ctx: Context,
  next: () => Promise<void>
) => {
  try {
    log.info("Business connection middleware started", {
      operation: "business_connection_middleware_start",
    });

    const update = ctx.update as unknown as TelegramBusinessMessageContext;

    console.log("ctx.update", JSON.stringify(ctx.update));

    if (!update?.business_message) {
      log.info("No business message found, skipping middleware", {
        operation: "business_connection_middleware",
      });
      await next();
      return;
    }

    const chat_id = update.business_message.chat.id;
    log.info("Processing business message", {
      operation: "business_connection_middleware",
      chat_id,
    });

    const userId = update.business_message.from?.id?.toString();
    if (!userId) {
      log.warn("No user ID found in business message", {
        operation: "business_connection_middleware",
        chat_id,
      });
      await next();
      return;
    }

    log.info("User ID extracted from business message", {
      operation: "business_connection_middleware",
      chat_id,
      userId,
    });

    const session = await getUserSession(userId);
    log.info("User session retrieved", {
      operation: "business_connection_middleware",
      chat_id,
      userId,
      hasSession: !!session,
      pendingOrderId: session?.pendingOrderId,
    });

    const pendingOrderId = session?.pendingOrderId;

    if (!pendingOrderId) {
      log.warn("No pending order found for user", {
        operation: "business_connection_middleware",
        chat_id,
        userId,
      });
      await ctx.telegram.sendMessage(chat_id, "No pending order found");
      await next();
      return;
    }

    const giftToTransfer = getGiftToTransfer(ctx);
    const flowContext: FlowContext = { ctx, chat_id, userId, pendingOrderId };

    log.info("Gift ID extraction result", {
      operation: "business_connection_middleware",
      chat_id,
      userId,
      pendingOrderId,
      giftIdToTransfer: giftToTransfer,
    });

    if (giftToTransfer) {
      // Flow 1: User wants to send their gift if the order is paid by buyer
      // OR Flow 3: User wants to activate their order and they need to send the gift beforehand
      const success = await handleSendGiftToPaidOrder(
        flowContext,
        giftToTransfer
      );

      if (success) {
        await next();
        return;
      }

      // If paid order flow failed, try activation flow
      const activationSuccess = await handleActivateOrderWithGift(
        flowContext,
        giftToTransfer
      );

      if (activationSuccess) {
        await next();
        return;
      }

      // Both flows failed, already handled in the flow functions
      await next();
      return;
    }

    // Flow 2: Buyer wants to get a gift
    // OR Flow 4: User wants to get back the cancelled gift
    log.info(
      "No gift to transfer, checking for buyer gift request or cancelled gift retrieval",
      {
        operation: "business_connection_middleware",
        chat_id,
        userId,
        pendingOrderId,
      }
    );

    // Try buyer get gift flow first
    const buyerSuccess = await handleBuyerGetGift(flowContext);

    if (buyerSuccess) {
      await next();
      return;
    }

    // If buyer flow failed, try cancelled gift retrieval flow
    const cancelledGiftSuccess = await handleGetCancelledGift(flowContext);

    if (cancelledGiftSuccess) {
      await next();
      return;
    }

    // Both flows failed, already handled in the flow functions
    log.warn("No applicable flow found for user request", {
      operation: "business_connection_middleware",
      chat_id,
      userId,
      pendingOrderId,
    });

    log.info("Business connection middleware completed", {
      operation: "business_connection_middleware_end",
      chat_id,
      userId,
      pendingOrderId,
    });

    await next();
  } catch (error) {
    log.error("Error handling update", error, {
      operation: "business_connection_middleware",
    });
    await next();
  }
};

export { handleGiftToRelayer } from "../flows/shared/gift-to-relayer-handler";
