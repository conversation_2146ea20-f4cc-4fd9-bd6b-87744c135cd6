import { defineMessages } from 'react-intl';

export const unifiedGiftInfoDrawerMessages = defineMessages({
  sendGiftToRelayer: {
    id: 'unifiedGiftInfoDrawer.sendGiftToRelayer',
    defaultMessage: 'Send Gift to Relayer',
  },
  claimYourGift: {
    id: 'unifiedGiftInfoDrawer.claimYourGift',
    defaultMessage: 'Claim Your Gift',
  },
  activateYourOrder: {
    id: 'unifiedGiftInfoDrawer.activateYourOrder',
    defaultMessage: 'Activate Your Order',
  },
  getCancelledGift: {
    id: 'unifiedGiftInfoDrawer.getCancelledGift',
    defaultMessage: 'Get Cancelled Gift',
  },
  sendGiftSteps: {
    id: 'unifiedGiftInfoDrawer.sendGiftSteps',
    defaultMessage: 'Follow these steps to send your gift to the relayer',
  },
  claimGiftSteps: {
    id: 'unifiedGiftInfoDrawer.claimGiftSteps',
    defaultMessage: 'Follow these steps to claim your gift from the relayer',
  },
  activateOrderSteps: {
    id: 'unifiedGiftInfoDrawer.activateOrderSteps',
    defaultMessage:
      'Follow these steps to activate your order by sending the gift',
  },
  getCancelledGiftSteps: {
    id: 'unifiedGiftInfoDrawer.getCancelledGiftSteps',
    defaultMessage: 'Follow these steps to retrieve your cancelled gift',
  },
  // Instructions
  goToBot: {
    id: 'unifiedGiftInfoDrawer.goToBot',
    defaultMessage: 'Go to {botLink}',
  },
  pressMySellOrders: {
    id: 'unifiedGiftInfoDrawer.pressMySellOrders',
    defaultMessage: 'Press on "My Sell Orders" button',
  },
  pressMySellOrdersPaid: {
    id: 'unifiedGiftInfoDrawer.pressMySellOrdersPaid',
    defaultMessage:
      'Press on "My Sell Orders" button and select from "Paid Orders" group',
  },
  pressMySellOrdersWaitingActivation: {
    id: 'unifiedGiftInfoDrawer.pressMySellOrdersWaitingActivation',
    defaultMessage:
      'Press on "My Sell Orders" button and select from "Waiting for Activation" group',
  },
  pressMySellOrdersCancelled: {
    id: 'unifiedGiftInfoDrawer.pressMySellOrdersCancelled',
    defaultMessage:
      'Press on "My Sell Orders" button and select from "Cancelled" group',
  },
  pressMuyBuyOrders: {
    id: 'unifiedGiftInfoDrawer.pressMuyBuyOrders',
    defaultMessage: 'Press on "My Buy Orders" button',
  },
  selectOrderToSend: {
    id: 'unifiedGiftInfoDrawer.selectOrderToSend',
    defaultMessage: 'Select your order you want to send',
  },
  selectOrderToGet: {
    id: 'unifiedGiftInfoDrawer.selectOrderToGet',
    defaultMessage: 'Select your order you want to get',
  },
  selectOrderToActivate: {
    id: 'unifiedGiftInfoDrawer.selectOrderToActivate',
    defaultMessage: 'Select your order you want to activate',
  },
  confirmAndSendToRelayer: {
    id: 'unifiedGiftInfoDrawer.confirmAndSendToRelayer',
    defaultMessage: 'Confirm action, and send this gift to {relayerLink}',
  },
  sendGiftToRelayerToActivate: {
    id: 'unifiedGiftInfoDrawer.sendGiftToRelayerToActivate',
    defaultMessage: 'Send your gift to {relayerLink} to activate this order',
  },
  confirmAndGoToRelayer: {
    id: 'unifiedGiftInfoDrawer.confirmAndGoToRelayer',
    defaultMessage: 'Confirm action, and go to {relayerLink} to get your gift',
  },
  goToRelayerToRetrieve: {
    id: 'unifiedGiftInfoDrawer.goToRelayerToRetrieve',
    defaultMessage: 'Go to {relayerLink} to retrieve your gift',
  },
});
