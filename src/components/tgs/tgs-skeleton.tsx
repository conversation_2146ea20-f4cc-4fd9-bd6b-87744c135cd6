'use client';

import { cn } from '@/lib/utils';

interface TgsSkeletonProps {
  className?: string;
  style?: React.CSSProperties;
}

export function TgsSkeleton({ className, style }: TgsSkeletonProps) {
  return (
    <div
      className={cn(
        'flex w-full h-full relative items-center justify-center bg-[#17212b] rounded-lg animate-pulse',
        className,
      )}
      style={style}
    >
      <div className="w-full h-full bg-[#3a4a5c] animate-pulse rounded-lg" />
    </div>
  );
}
