'use client';

import { <PERSON><PERSON>, Player } from '@lottiefiles/react-lottie-player';
import { ungzip } from 'pako';
import { useEffect, useState } from 'react';

import { getCachedTgsData, setCachedTgsData } from '@/components/tgs/tgs-cache';
import { TgsSkeleton } from '@/components/tgs/tgs-skeleton';

interface TgsViewerProps {
  tgsUrl: string;
  style?: React.CSSProperties;
  className?: string;
  autoplay?: boolean;
  loop?: boolean;
  showControls?: boolean;
  onError?: (error: string) => void;
  onLoad?: () => void;
}

export const TgsViewer = ({
  tgsUrl,
  style = { height: '200px', width: '200px' },
  className = '',
  autoplay = true,
  loop = true,
  showControls = false,
  onError,
  onLoad,
}: TgsViewerProps) => {
  const [lottieJson, setLottieJson] = useState<object | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(false);

  useEffect(() => {
    if (!tgsUrl) {
      setLottieJson(null);
      setError(null);
      return;
    }

    setError(null);
    setLoading(true);

    if (!tgsUrl.endsWith('.tgs')) {
      setError('Invalid URL: The URL does not end with .tgs');
      setLottieJson(null);
      setLoading(false);
      return;
    }

    const cachedData = getCachedTgsData(tgsUrl);
    if (cachedData) {
      setLottieJson(cachedData);
      setLoading(false);
      onLoad?.();
      console.log('TGS file loaded from cache:', tgsUrl);
      return;
    }

    fetch(tgsUrl)
      .then((response) => {
        if (!response.ok) {
          throw new Error(`Network response was not ok (${response.status})`);
        }
        return response.arrayBuffer();
      })
      .then((buffer) => {
        if (buffer instanceof ArrayBuffer) {
          try {
            const decompressed = ungzip(new Uint8Array(buffer));
            const data = new TextDecoder('utf-8').decode(decompressed);
            const lottieJsonData: object = JSON.parse(data);

            setCachedTgsData(tgsUrl, lottieJsonData);

            setLottieJson(lottieJsonData);
            onLoad?.();
            console.log('TGS file loaded and cached successfully:', tgsUrl);
          } catch (err) {
            console.error('TGS decompression error:', err);
            throw new Error('Error decompressing or parsing the .tgs file.');
          }
        } else {
          throw new Error('Received data is not an ArrayBuffer.');
        }
      })
      .catch((error: Error) => {
        console.error('Error fetching or parsing .tgs file:', error);
        onError?.(error.message);
        setError(error.message);
        setLottieJson(null);
      })
      .finally(() => {
        setLoading(false);
      });
  }, [tgsUrl]);

  if (loading || !lottieJson) {
    return <TgsSkeleton className={className} style={style} />;
  }

  if (error) {
    return (
      <div
        className={`flex items-center justify-center bg-gray-100 rounded-lg ${className}`}
        style={style}
      >
        <div className="text-center p-4">
          <div className="text-red-500 text-sm mb-2">⚠️</div>
          <div className="text-xs text-gray-600">{error}</div>
        </div>
      </div>
    );
  }

  if (!lottieJson) {
    return (
      <div
        className={`flex items-center justify-center bg-gray-100 rounded-lg ${className}`}
        style={style}
      >
        <div className="text-gray-500 text-sm">No animation data</div>
      </div>
    );
  }

  return (
    <div className={className}>
      <Player autoplay={autoplay} loop={loop} src={lottieJson} style={style}>
        {showControls && (
          <Controls
            visible={true}
            buttons={['play', 'repeat', 'frame', 'debug']}
          />
        )}
      </Player>
    </div>
  );
};
