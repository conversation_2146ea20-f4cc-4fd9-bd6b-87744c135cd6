'use client';

import { Loader2 } from 'lucide-react';
import { forwardRef } from 'react';

import type { TabType } from '@/app/(app)/marketplace/use-marketplace-orders';
import { OrderCardSkeletonGrid } from '@/components/ui/order-card-skeleton';
import { VirtualizedCard } from '@/components/ui/virtualized-card';
import { ItemCacheProvider } from '@/components/ui/virtualized-grid';
import type {
  CollectionEntity,
  OrderEntity,
  UserType,
} from '@/mikerudenko/marketplace-shared';

interface BaseMarketplaceOrderListProps {
  orders: OrderEntity[];
  loading: boolean;
  loadingMore: boolean;
  hasMore: boolean;
  emptyMessage: string;
  onOrderClick: (order: OrderEntity) => void;
  gridCols?: string;
}

interface StandardOrderListProps extends BaseMarketplaceOrderListProps {
  variant: 'order';
  collections: CollectionEntity[];
  activeTab?: TabType;
}

interface UserOrderListProps extends BaseMarketplaceOrderListProps {
  variant: 'user-order';
  getUserRole: (order: OrderEntity) => UserType;
  onSendAGiftClick?: (order: OrderEntity) => void;
  onGetAGiftClick?: (order: OrderEntity) => void;
  onResellOrder?: (order: OrderEntity) => void;
  onActivateOrder?: (order: OrderEntity) => void;
  onGetCancelledGift?: (order: OrderEntity) => void;
}

export type MarketplaceOrderListProps =
  | StandardOrderListProps
  | UserOrderListProps;

export const MarketplaceOrderList = forwardRef<
  HTMLDivElement,
  MarketplaceOrderListProps
>((props, ref) => {
  const {
    orders,
    loading,
    loadingMore,
    hasMore,
    emptyMessage,
    onOrderClick,
    variant,
    gridCols = 'grid-cols-2 md:grid-cols-4',
  } = props;

  if (loading) {
    return <OrderCardSkeletonGrid count={8} gridCols={gridCols} />;
  }

  if (orders.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-[#708499]">{emptyMessage}</p>
      </div>
    );
  }

  const renderCard = (order: OrderEntity, index: number) => {
    const baseProps = {
      order,
      onClick: () => onOrderClick(order),
      index,
      initialRenderedCount: 8,
    };

    if (variant === 'user-order') {
      const userOrderProps = props;
      const userRole = userOrderProps.getUserRole(order);
      return (
        <VirtualizedCard
          key={`${variant}-${order.id}-${index}`}
          {...baseProps}
          variant={'user-order' as const}
          userRole={userRole}
          onSendAGiftClick={
            userOrderProps.onSendAGiftClick
              ? () => userOrderProps.onSendAGiftClick!(order)
              : undefined
          }
          onGetAGiftClick={
            userOrderProps.onGetAGiftClick
              ? () => userOrderProps.onGetAGiftClick!(order)
              : undefined
          }
          onResellOrder={
            userOrderProps.onResellOrder
              ? () => userOrderProps.onResellOrder!(order)
              : undefined
          }
          onActivateOrder={
            userOrderProps.onActivateOrder
              ? () => userOrderProps.onActivateOrder!(order)
              : undefined
          }
          onGetCancelledGift={
            userOrderProps.onGetCancelledGift
              ? () => userOrderProps.onGetCancelledGift!(order)
              : undefined
          }
        />
      );
    }

    const standardProps = props;
    return (
      <VirtualizedCard
        {...baseProps}
        key={`${variant}-${order.id}-${index}`}
        variant={variant}
        collection={standardProps.collections.find(
          (c) => c.id === order.collectionId,
        )}
        activeTab={standardProps.activeTab}
      />
    );
  };

  return (
    <>
      <ItemCacheProvider>
        <div className={`grid ${gridCols} gap-2`}>
          {orders.map((order, index) => renderCard(order, index))}
        </div>
      </ItemCacheProvider>

      {orders.length > 0 && (
        <div
          ref={ref}
          className="flex justify-center py-4 w-full"
          style={{
            height: '60px',
            minHeight: '60px',
            backgroundColor: 'transparent',
          }}
        >
          {loadingMore && hasMore && (
            <div className="flex items-center gap-2 text-gray-400">
              <Loader2 className="w-4 h-4 animate-spin" />
              <span>Loading more orders...</span>
            </div>
          )}
        </div>
      )}
    </>
  );
});
